import { ReportDiffReviewEventDto } from '@repo/api/generated-client/snake-case/models/ReportDiffReviewEventDto';
import { DiffTransformUtils, markdownSerializer } from '@repo/editor-common';
import { IBussinessDiffTransformUtils } from '@repo/ui-business-editor';
import { Editor } from '@tiptap/core';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { getWorkflow } from './thought-adpater-extension';

export class DiffTransformUtilsWrapper
  extends DiffTransformUtils
  implements IBussinessDiffTransformUtils
{
  constructor(private editor: Editor) {
    super();
  }

  async reportWriterData(params: Omit<ReportDiffReviewEventDto, 'thought_id'>) {
     const reportParams:  ReportDiffReviewEventDto = {
        ...params,
        thought_id: this.workflow?.getThoughtId() || '',
        resolve_version: {
          thought_title: this.workflow?.title || '',
          content: {
            plain: markdownSerializer.serialize(this.editor.state.doc),
            raw: ,
          },
        },
      };
    await callAPI(apiClient.diffApi.reportDiffReviewEvent(reportParams), {
      silent: true,
    });
    return;
  }

  get workflow() {
    return getWorkflow(this.editor);
  }
}
